defmodule RepobotWeb.Live.Folders.Show do
  use RepobotWeb, :live_view
  require Logger

  import RepobotWeb.UI.Components
  alias Repobot.{Folders, SourceFiles, Files}
  alias Repobot.Repo

  def mount(%{"id" => id}, _session, socket) do
    folder = Folders.get_folder!(id)
    source_files = get_source_files(socket.assigns)

    # Preload files with content for all repositories (regular and template)
    all_repositories =
      (folder.repositories ++ folder.template_repositories)
      |> Enum.uniq_by(& &1.id)
      |> Files.load_repositories_with_files()

    # Update folder struct with combined, preloaded repositories
    folder = %{folder | repositories: all_repositories}

    # Subscribe to repository file changes if connected
    if connected?(socket) do
      Phoenix.PubSub.subscribe(Repobot.PubSub, "repository_files")
    end

    # Fetch source files created *from* each template repository
    source_files_from_templates =
      folder.template_repositories
      |> Enum.reduce(%{}, fn repo, acc ->
        files = SourceFiles.list_source_files_created_from_repository(repo)
        Map.put(acc, repo.id, files)
      end)

    socket =
      socket
      |> assign(:folder, folder)
      |> assign(:source_files, source_files)
      |> assign(:source_files_from_templates, source_files_from_templates)
      |> assign(:page_title, folder.name)
      |> assign(:show_edit_modal, false)
      |> assign(:editing_source_file, nil)
      |> assign(:show_diff_modal, false)
      |> assign(:diff_repository, nil)
      |> assign(:diff_source_file, nil)
      |> assign(:available_repositories, [])

    {:ok, socket}
  end

  def handle_event("move_to_folder", %{"source_file_id" => id}, socket) do
    source_file = SourceFiles.get_source_file!(id)

    # Add the source file to the current folder
    case SourceFiles.add_source_file_to_folder(source_file, socket.assigns.folder) do
      {:ok, _} ->
        source_files = get_source_files(socket.assigns)

        {:noreply,
         socket
         |> assign(:source_files, source_files)
         |> put_flash(:info, "Source file moved to folder")}

      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to move source file to folder")}
    end
  end

  def handle_event("add_source_file", %{"source_file_id" => source_file_id}, socket) do
    case Repo.get(SourceFile, source_file_id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "Source file not found")}

      source_file ->
        source_file = Repo.preload(source_file, [:category, :tags, :repositories, :pull_requests])

        results =
          socket.assigns.folder.repositories
          |> non_template_repositories()
          |> Enum.map(fn repository ->
            Repobot.Repositories.add_source_file(repository, source_file)
          end)

        success_count =
          Enum.count(results, fn
            {:ok, _} -> true
            _ -> false
          end)

        error_count =
          Enum.count(results, fn
            {:error, _} -> true
            _ -> false
          end)

        message =
          case {success_count, error_count} do
            {s, 0} -> "Successfully added source file to #{s} repositories"
            {0, e} -> "Failed to add source file to #{e} repositories"
            {s, e} -> "Added source file to #{s} repositories, failed for #{e} repositories"
          end

        # Refresh the folder data to get updated repository associations
        folder = Folders.get_folder!(socket.assigns.folder.id)

        {:noreply,
         socket
         |> assign(:folder, folder)
         |> put_flash(if(error_count > 0, do: :error, else: :info), message)}
    end
  end

  def handle_event("remove_source_file", %{"source_file_id" => source_file_id}, socket) do
    case Repo.get(SourceFile, source_file_id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "Source file not found")}

      source_file ->
        source_file = Repo.preload(source_file, [:category, :tags, :repositories, :pull_requests])

        results =
          Enum.map(socket.assigns.folder.repositories, fn repository ->
            Repobot.Repositories.remove_source_file(repository, source_file)
          end)

        success_count =
          Enum.count(results, fn
            {:ok, _} -> true
            _ -> false
          end)

        error_count =
          Enum.count(results, fn
            {:error, _} -> true
            _ -> false
          end)

        message =
          case {success_count, error_count} do
            {s, 0} -> "Successfully removed source file from #{s} repositories"
            {0, e} -> "Failed to remove source file from #{e} repositories"
            {s, e} -> "Removed source file from #{s} repositories, failed for #{e} repositories"
          end

        # Refresh the folder data to get updated repository associations
        folder = Folders.get_folder!(socket.assigns.folder.id)

        {:noreply,
         socket
         |> assign(:folder, folder)
         |> put_flash(if(error_count > 0, do: :error, else: :info), message)}
    end
  end

  def handle_event("move_to_global", %{"source_file_id" => id}, socket) do
    source_file = SourceFiles.get_source_file!(id, socket.assigns.current_organization.id)

    # Remove the source file from the current folder
    case SourceFiles.remove_source_file_from_folder(source_file, socket.assigns.folder) do
      {1, nil} ->
        source_files = get_source_files(socket.assigns)

        {:noreply,
         socket
         |> assign(:source_files, source_files)
         |> put_flash(:info, "Source file moved to global")}

      {0, nil} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to move source file to global")}
    end
  end

  def handle_event("show_edit_modal", %{"source_file_id" => id}, socket) do
    source_file = SourceFiles.get_source_file!(id)

    {:noreply,
     socket
     |> assign(:show_edit_modal, true)
     |> assign(:editing_source_file, source_file)}
  end

  def handle_event("hide_edit_modal", _, socket) do
    {:noreply,
     socket
     |> assign(:show_edit_modal, false)
     |> assign(:editing_source_file, nil)}
  end

  def handle_event("save_source_file", %{"content" => content}, socket) do
    case SourceFiles.update_source_file(socket.assigns.editing_source_file, %{content: content}) do
      {:ok, _source_file} ->
        source_files = get_source_files(socket.assigns)

        {:noreply,
         socket
         |> assign(:source_files, source_files)
         |> assign(:show_edit_modal, false)
         |> assign(:editing_source_file, nil)
         |> put_flash(:info, "Source file updated successfully")}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to update source file")}
    end
  end

  def handle_event(
        "show_diff",
        %{"source_file_id" => source_file_id, "repository" => repository},
        socket
      ) do
    [owner, repo] = String.split(repository, "/")
    source_file = SourceFiles.get_source_file!(source_file_id)

    # Get repository and its files from database
    repository =
      Repobot.Repository
      |> Repo.get_by!(owner: owner, name: repo)
      |> Repobot.Repo.preload([:folder])
      |> Files.load_repository_with_files()

    # Get the file content from our database
    repo_content =
      repository.files
      |> Enum.find(&(&1.path == source_file.target_path))
      |> case do
        file when not is_nil(file) -> Files.get_content(file)
        _ -> ""
      end

    # Render the source file template for this repository if it's a template
    rendered_content =
      if source_file.is_template do
        case SourceFiles.render_template_for_repository(source_file, repository) do
          {:ok, content} -> content
          {:error, _reason} -> Files.get_content(source_file)
        end
      else
        Files.get_content(source_file)
      end

    # Get all repositories that have this source file for comparison
    available_repositories =
      socket.assigns.folder.repositories
      |> Enum.reject(& &1.template)
      |> Enum.filter(fn repo ->
        # Include repositories that have this source file and are not the current repository
        repo.id != repository.id and
          Enum.any?(repo.source_files || [], &(&1.id == source_file.id))
      end)
      |> Enum.map(& &1.full_name)

    {:noreply,
     socket
     |> assign(:show_diff_modal, true)
     |> assign(:diff_repository, repository)
     |> assign(:diff_source_file, source_file)
     |> assign(:available_repositories, available_repositories)
     |> push_event("render_diff", %{source: rendered_content, target: repo_content || ""})}
  end

  def handle_event("change_diff_repository", %{"repository" => repository}, socket) do
    [owner, repo] = String.split(repository, "/")
    source_file = socket.assigns.diff_source_file

    # Get repository and its files from database
    repository =
      Repobot.Repository
      |> Repo.get_by!(owner: owner, name: repo)
      |> Repobot.Repo.preload([:folder])
      |> Files.load_repository_with_files()

    # Get the file content from our database
    repo_content =
      repository.files
      |> Enum.find(&(&1.path == source_file.target_path))
      |> case do
        file when not is_nil(file) -> Files.get_content(file)
        _ -> ""
      end

    # Render the source file template for this repository if it's a template
    rendered_content =
      if source_file.is_template do
        case SourceFiles.render_template_for_repository(source_file, repository) do
          {:ok, content} -> content
          {:error, _reason} -> Files.get_content(source_file)
        end
      else
        Files.get_content(source_file)
      end

    # Get all repositories that have this source file for comparison
    available_repositories =
      socket.assigns.folder.repositories
      |> Enum.reject(& &1.template)
      |> Enum.filter(fn repo ->
        # Include repositories that have this source file and are not the current repository
        repo.id != repository.id and
          Enum.any?(repo.source_files || [], &(&1.id == socket.assigns.diff_source_file.id))
      end)
      |> Enum.map(& &1.full_name)

    {:noreply,
     socket
     |> assign(:show_diff_modal, true)
     |> assign(:diff_repository, repository)
     |> assign(:diff_source_file, source_file)
     |> assign(:available_repositories, available_repositories)
     |> push_event("render_diff", %{source: rendered_content, target: repo_content || ""})}
  end

  def handle_event("hide_diff_modal", _, socket) do
    {:noreply,
     socket
     |> assign(:show_diff_modal, false)
     |> assign(:diff_repository, nil)
     |> assign(:diff_source_file, nil)
     |> assign(:available_repositories, [])}
  end

  def handle_event("delete_folder", _, socket) do
    case Folders.delete_folder(socket.assigns.folder) do
      {:ok, _folder} ->
        {:noreply,
         socket
         |> put_flash(:info, "Folder deleted successfully")
         |> push_navigate(to: ~p"/repositories")}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to delete folder")}
    end
  end

  # Forward CommonFiles component events that might not be properly targeted
  def handle_event("show_import_dropdown", _params, socket) do
    # This event should be handled by the CommonFiles component
    # If it reaches here, it means the event wasn't properly targeted
    {:noreply, socket}
  end

  def handle_event("hide_import_dropdown", _params, socket) do
    # This event should be handled by the CommonFiles component
    {:noreply, socket}
  end

  def handle_event("import_common_file", _params, socket) do
    # This event should be handled by the CommonFiles component
    {:noreply, socket}
  end

  def handle_event("show_file_preview", _params, socket) do
    # This event should be handled by the CommonFiles component
    {:noreply, socket}
  end

  def handle_event("hide_file_preview", _params, socket) do
    # This event should be handled by the CommonFiles component
    {:noreply, socket}
  end

  def handle_event("generate_template", _params, socket) do
    # This event should be handled by the CommonFiles component
    {:noreply, socket}
  end

  def handle_event("refresh_common_files", _params, socket) do
    # This event should be handled by the CommonFiles component
    {:noreply, socket}
  end

  def handle_event("toggle_star", _, socket) do
    case Folders.toggle_starred(socket.assigns.folder) do
      {:ok, folder} ->
        {:noreply, assign(socket, :folder, folder)}

      {:error, _changeset} ->
        {:noreply, socket}
    end
  end

  # Handle events forwarded from FolderSourceFiles component
  def handle_info({:folder_source_files_event, event_name, params}, socket) do
    case event_name do
      "refresh_folder" ->
        # Refresh the folder data to get updated repository associations
        folder = Folders.get_folder!(socket.assigns.folder.id)
        {:noreply, assign(socket, :folder, folder)}

      "refresh_source_files" ->
        # Refresh source files
        source_files = get_source_files(socket.assigns)
        {:noreply, assign(socket, :source_files, source_files)}

      "flash_message" ->
        # Handle flash messages from component
        %{type: type, message: message} = params
        {:noreply, put_flash(socket, type, message)}

      # These events are now handled internally by the component
      event_name
      when event_name in [
             "show_diff",
             "move_to_global",
             "add_source_file",
             "remove_source_file",
             "show_edit_modal",
             "hide_edit_modal",
             "save_source_file",
             "change_diff_repository",
             "hide_diff_modal"
           ] ->
        {:noreply, socket}
    end
  end

  # Handle events forwarded from GlobalSourceFiles component
  def handle_info({:global_source_files_event, event_name, params}, socket) do
    case event_name do
      "refresh_folder" ->
        # Refresh the folder data to get updated repository associations
        folder = Folders.get_folder!(socket.assigns.folder.id)
        {:noreply, assign(socket, :folder, folder)}

      "refresh_source_files" ->
        # Refresh source files
        source_files = get_source_files(socket.assigns)
        {:noreply, assign(socket, :source_files, source_files)}

      "flash_message" ->
        # Handle flash messages from component
        %{type: type, message: message} = params
        {:noreply, put_flash(socket, type, message)}

      # Forward modal-related events to existing handlers
      event_name when event_name in ["show_diff", "show_edit_modal"] ->
        handle_event(event_name, params, socket)
    end
  end

  # Handle events forwarded from CommonFiles component
  def handle_info({:common_files_event, event_name, params}, socket) do
    case event_name do
      "refresh_folder" ->
        # Refresh the folder data to get updated repository associations
        folder = Folders.get_folder!(socket.assigns.folder.id)
        {:noreply, assign(socket, :folder, folder)}

      "refresh_source_files" ->
        # Refresh source files
        source_files = get_source_files(socket.assigns)
        {:noreply, assign(socket, :source_files, source_files)}

      "flash_message" ->
        # Handle flash messages from component
        %{type: type, message: message} = params
        {:noreply, put_flash(socket, type, message)}

      "calculate_common_files" ->
        # This is handled by the CommonFiles component itself
        {:noreply, socket}
    end
  end

  # Handle events forwarded from Repositories component
  def handle_info({:repositories_event, event_name, params}, socket) do
    case event_name do
      "trees_loaded" ->
        # Trees are loaded, trigger CommonFiles component calculation
        send_update(RepobotWeb.Live.Folders.Components.CommonFiles,
          id: "common-files",
          trigger_calculation: true
        )

        {:noreply, socket}

      "content_refreshed" ->
        # Content refresh is complete, this is handled by the main notification handler
        {:noreply, socket}

      "loading_error" ->
        # Handle loading error
        %{reason: reason} = params
        {:noreply, put_flash(socket, :error, "Failed to load repository files: #{reason}")}

      # Forward modal-related events to existing handlers
      event_name when event_name in ["show_diff", "show_edit_modal"] ->
        handle_event(event_name, params, socket)
    end
  end

  # Handle similarity progress messages from CommonFiles component background processing
  def handle_info({:similarity_progress, progress}, socket) do
    # Forward to CommonFiles component
    send_update(RepobotWeb.Live.Folders.Components.CommonFiles,
      id: "common-files",
      similarity_progress: progress
    )

    {:noreply, socket}
  end

  def handle_info({:similarity_complete, files}, socket) do
    # Forward to CommonFiles component
    send_update(RepobotWeb.Live.Folders.Components.CommonFiles,
      id: "common-files",
      similarity_complete: files
    )

    {:noreply, socket}
  end

  def handle_info({:similarity_error, reason}, socket) do
    # Forward to CommonFiles component
    send_update(RepobotWeb.Live.Folders.Components.CommonFiles,
      id: "common-files",
      similarity_error: reason
    )

    {:noreply, socket}
  end

  # Handle Oban.Notifier messages for CommonFiles component
  def handle_info(
        {:notification, channel,
         %{"event" => "repository_files_progress", "progress" => progress, "message" => _message}},
        socket
      )
      when is_atom(channel) do
    # Check if this is a repository files channel for this user
    if Atom.to_string(channel)
       |> String.starts_with?("repository_files:#{socket.assigns.current_user.id}") do
      # Forward to CommonFiles component
      send_update(RepobotWeb.Live.Folders.Components.CommonFiles,
        id: "common-files",
        content_refresh_progress: progress
      )
    end

    {:noreply, socket}
  end

  def handle_info(
        {:notification, channel, %{"event" => "repository_files_complete", "result" => result}},
        socket
      )
      when is_atom(channel) do
    # Check if this is a repository files channel for this user
    if Atom.to_string(channel)
       |> String.starts_with?("repository_files:#{socket.assigns.current_user.id}") do
      # Forward to CommonFiles component
      send_update(RepobotWeb.Live.Folders.Components.CommonFiles,
        id: "common-files",
        oban_result: result
      )
    end

    {:noreply, socket}
  end

  # Handle repository files updated events from push webhooks
  def handle_info({:repository_files_updated, repository_id, _metadata}, socket) do
    # Check if any of the repositories in this folder were updated
    folder_repo_ids =
      (socket.assigns.folder.repositories ++ socket.assigns.folder.template_repositories)
      |> Enum.map(& &1.id)
      |> MapSet.new()

    if MapSet.member?(folder_repo_ids, repository_id) do
      # One of our repositories was updated, trigger CommonFiles component recalculation
      send_update(RepobotWeb.Live.Folders.Components.CommonFiles,
        id: "common-files",
        trigger_calculation: true
      )

      {:noreply, put_flash(socket, :info, "Repository files updated automatically")}
    else
      {:noreply, socket}
    end
  end

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <div class="mb-8">
        <nav class="mb-6">
          <ol role="list" class="flex items-center space-x-2 text-sm text-slate-500">
            <li>
              <.link navigate={~p"/repositories"} class="link link-hover">
                Repositories
              </.link>
            </li>
            <li>•</li>
            <li class="font-medium text-slate-900">{@folder.name}</li>
          </ol>
        </nav>
        <div class="flex justify-between items-center mb-8">
          <div class="flex items-center gap-2">
            <h1 class="text-2xl font-semibold text-slate-900">{@folder.name}</h1>
            <button
              phx-click="toggle_star"
              class={"text-slate-400 hover:text-slate-500 #{if @folder.starred, do: "text-yellow-400 hover:text-yellow-500"}"}
              aria-label={if @folder.starred, do: "Unstar folder", else: "Star folder"}
            >
              <.icon name="hero-star" class="w-6 h-6" />
            </button>
          </div>
          <div class="flex items-center gap-2">
            <.btn
              href={~p"/folders/#{@folder}/edit"}
              data-phx-link="redirect"
              data-phx-link-state="push"
              class="inline-flex items-center px-3 py-2"
              variant="soft"
            >
              <.icon name="hero-pencil" /> Edit
            </.btn>
            <.btn
              phx-click="delete_folder"
              data-confirm="Are you sure you want to delete this folder? This will unassign all repositories from this folder."
              variant="error"
            >
              <.icon name="hero-trash" /> Delete
            </.btn>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div class="lg:col-span-1">
          <.content>
            <:header>Repositories</:header>
            <:body>
              <.live_component
                module={RepobotWeb.Live.Folders.Components.Repositories}
                id="repositories"
                folder={@folder}
                current_user={@current_user}
              />
            </:body>
          </.content>
        </div>

        <div class="lg:col-span-3">
          <!-- Tab-based interface for file sections -->
          <div class="bg-white rounded-lg shadow-sm border border-slate-200">
            <!-- Tab Navigation -->
            <div class="border-b border-slate-200 bg-slate-50">
              <nav class="flex space-x-8 px-6" aria-label="Tabs">
                <button
                  type="button"
                  phx-click={
                    JS.show(to: "#folder-source-files-content")
                    |> JS.hide(to: "#global-source-files-content")
                    |> JS.hide(to: "#common-files-content")
                    |> JS.add_class("border-indigo-500 text-indigo-600", to: "#folder-tab")
                    |> JS.remove_class("border-indigo-500 text-indigo-600", to: "#global-tab")
                    |> JS.remove_class("border-indigo-500 text-indigo-600", to: "#common-tab")
                    |> JS.add_class(
                      "border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300",
                      to: "#global-tab"
                    )
                    |> JS.add_class(
                      "border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300",
                      to: "#common-tab"
                    )
                    |> JS.remove_class(
                      "border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300",
                      to: "#folder-tab"
                    )
                  }
                  id="folder-tab"
                  class="border-indigo-500 text-indigo-600 whitespace-nowrap py-4 px-2 border-b-2 font-medium text-base flex items-center cursor-pointer"
                >
                  <.icon name="hero-folder" class="w-4 h-4 me-2" /> Folder Source Files
                </button>
                <button
                  type="button"
                  phx-click={
                    JS.show(to: "#global-source-files-content")
                    |> JS.hide(to: "#folder-source-files-content")
                    |> JS.hide(to: "#common-files-content")
                    |> JS.add_class("border-indigo-500 text-indigo-600", to: "#global-tab")
                    |> JS.remove_class("border-indigo-500 text-indigo-600", to: "#folder-tab")
                    |> JS.remove_class("border-indigo-500 text-indigo-600", to: "#common-tab")
                    |> JS.add_class(
                      "border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300",
                      to: "#folder-tab"
                    )
                    |> JS.add_class(
                      "border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300",
                      to: "#common-tab"
                    )
                    |> JS.remove_class(
                      "border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300",
                      to: "#global-tab"
                    )
                  }
                  id="global-tab"
                  class="border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 whitespace-nowrap py-4 px-2 border-b-2 font-medium text-base flex items-center cursor-pointer"
                >
                  <.icon name="hero-globe-alt" class="w-4 h-4 me-2" /> Global Source Files
                </button>
                <button
                  type="button"
                  phx-click={
                    JS.show(to: "#common-files-content")
                    |> JS.hide(to: "#folder-source-files-content")
                    |> JS.hide(to: "#global-source-files-content")
                    |> JS.add_class("border-indigo-500 text-indigo-600", to: "#common-tab")
                    |> JS.remove_class("border-indigo-500 text-indigo-600", to: "#folder-tab")
                    |> JS.remove_class("border-indigo-500 text-indigo-600", to: "#global-tab")
                    |> JS.add_class(
                      "border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300",
                      to: "#folder-tab"
                    )
                    |> JS.add_class(
                      "border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300",
                      to: "#global-tab"
                    )
                    |> JS.remove_class(
                      "border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300",
                      to: "#common-tab"
                    )
                  }
                  id="common-tab"
                  class="border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 whitespace-nowrap py-4 px-2 border-b-2 font-medium text-base flex items-center cursor-pointer"
                >
                  <.icon name="hero-document-duplicate" class="w-4 h-4 me-2" /> Common Files
                </button>
              </nav>
            </div>
            
    <!-- Tab 1: Folder Source Files -->
            <div id="folder-source-files-content" class="">
              <.live_component
                module={RepobotWeb.Live.Folders.Components.FolderSourceFiles}
                id="folder-source-files"
                folder={@folder}
                source_files={@source_files}
                current_organization={@current_organization}
              />
            </div>
            
    <!-- Tab 2: Global Source Files -->
            <div id="global-source-files-content" class="hidden">
              <.live_component
                module={RepobotWeb.Live.Folders.Components.GlobalSourceFiles}
                id="global-source-files"
                folder={@folder}
                source_files={@source_files}
                current_organization={@current_organization}
              />
            </div>
            
    <!-- Tab 3: Common Files -->
            <div id="common-files-content" class="hidden">
              <.live_component
                module={RepobotWeb.Live.Folders.Components.CommonFiles}
                id="common-files"
                folder={@folder}
                source_files={@source_files}
                current_organization={@current_organization}
                current_user={@current_user}
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <%= if @show_edit_modal and @editing_source_file do %>
      <div class="fixed inset-0 bg-slate-500/75 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4">
          <div class="px-6 py-4 border-b border-slate-200 flex items-center justify-between">
            <h3 class="text-lg font-medium text-slate-900">
              Edit {@editing_source_file.name}
            </h3>
            <.btn phx-click="hide_edit_modal" variant="ghost" size="sm" circle>
              <.icon name="hero-x-mark" class="w-5 h-5" />
            </.btn>
          </div>
          <div class="p-6">
            <.form for={%{}} id="edit-source-file-form" phx-submit="save_source_file">
              <div class="space-y-4">
                <div>
                  <fieldset class="fieldset">
                    <label class="label">Content</label>
                    <textarea name="content" class="textarea font-mono" rows="20"><%= Files.get_content(@editing_source_file) %></textarea>
                  </fieldset>
                </div>
              </div>
              <div class="mt-6 flex justify-end gap-3">
                <.btn type="button" phx-click="hide_edit_modal" variant="outline">
                  Cancel
                </.btn>
                <.btn type="submit" variant="primary">
                  Save Changes
                </.btn>
              </div>
            </.form>
          </div>
        </div>
      </div>
    <% end %>

    <%= if @show_diff_modal do %>
      <div
        class="fixed inset-0 bg-slate-500/75 flex items-center justify-center z-50"
        phx-window-keydown="hide_diff_modal"
        phx-key="escape"
      >
        <div class="bg-white rounded-lg shadow-xl max-w-7xl w-full mx-4 max-h-[90vh] flex flex-col">
          <div class="px-6 py-4 border-b border-slate-200 flex items-center justify-between">
            <div class="flex items-center gap-4">
              <h3 class="text-lg font-medium text-slate-900">
                File diff for {@diff_repository.full_name}
              </h3>
              <%= if not Enum.empty?(@available_repositories) do %>
                <div class="relative">
                  <form phx-change="change_diff_repository" class="flex items-center">
                    <select name="repository" class="select">
                      <option value="" disabled selected>Compare with...</option>
                      <%= for repo <- @available_repositories do %>
                        <option value={repo}>{repo}</option>
                      <% end %>
                    </select>
                  </form>
                </div>
              <% end %>
            </div>
            <.btn phx-click="hide_diff_modal" variant="ghost" size="sm" circle>
              <.icon name="hero-x-mark" class="w-5 h-5" />
            </.btn>
          </div>
          <div class="p-6 overflow-auto flex-1" id="diff-container" phx-hook="Diff"></div>
        </div>
      </div>
    <% end %>
    """
  end

  defp get_source_files(%{current_user: user, current_organization: organization}) do
    SourceFiles.list_source_files(user, organization)
  end

  defp non_template_repositories(repositories) do
    repositories
    |> Enum.reject(& &1.template)
    |> Enum.sort_by(& &1.full_name)
  end
end
